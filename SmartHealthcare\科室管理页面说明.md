# 科室管理页面说明

## 功能概述
科室管理页面是智慧医疗系统中的一个重要模块，用于管理医院的科室信息和病例信息。

## 页面布局
页面采用左右分栏布局：
- **左侧**：科室和病例的树形显示区域
- **右侧**：科室和病例的添加功能区域

## 功能详细说明

### 左侧 - 科室树形显示
1. **科室显示**：
   - 调用后台API：`/api/SmartHealthcare/Handle`
   - 显示所有科室的列表
   - 默认情况下不显示病例

2. **病例显示**：
   - 点击科室后，会展开显示该科室下的病例
   - 病例数据通过API动态加载
   - 支持树形结构的展开和收缩

### 右侧 - 添加功能区域

#### 科室添加（上方区域）
**输入字段**：
- 科别名称：科室的名称
- 科室地址：科室的物理位置
- 挂号费：该科室的挂号费用

**操作按钮**：
- **添加**：调用API `/api/SmartHealthcare/CreateDepartment` 创建新科室
- **清除**：清空所有输入字段
- **修改科室**：预留的修改功能按钮

#### 病例添加（下方区域）
**输入字段**：
- 科别：下拉选择框，调用API `/api/SmartHealthcare/GetDepartment` 获取科室列表
- 名称：病例的名称

**操作按钮**：
- **添加**：创建新的病例记录
- **删除**：清空病例输入字段
- **取消**：关闭整个科室管理窗口

## API接口说明

### 1. 获取科室列表
- **接口**：`GET /api/SmartHealthcare/Handle`
- **用途**：获取所有科室信息用于左侧树形显示
- **返回**：科室列表数据

### 2. 创建科室
- **接口**：`POST /api/SmartHealthcare/CreateDepartment`
- **参数**：
  ```json
  {
    "Name": "科室名称",
    "Address": "科室地址", 
    "RegistrationFee": 挂号费数值
  }
  ```

### 3. 获取科室下拉列表
- **接口**：`GET /api/SmartHealthcare/GetDepartment`
- **用途**：为病例添加功能提供科室选择选项
- **返回**：科室ID和名称的列表

### 4. 创建病例
- **接口**：`POST /api/SmartHealthcare/CreateMedicalCase`
- **参数**：
  ```json
  {
    "DepartmentId": 科室ID,
    "Name": "病例名称"
  }
  ```

## 数据模型

### Department（科室）
```csharp
public class Department
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Address { get; set; }
    public decimal RegistrationFee { get; set; }
}
```

### MedicalCase（病例）
```csharp
public class MedicalCase
{
    public int Id { get; set; }
    public string Name { get; set; }
    public int DepartmentId { get; set; }
}
```

### ApiResponse（API响应）
```csharp
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public T Data { get; set; }
}
```

## 使用方法

1. **启动应用程序**
2. **进入系统管理页面**
3. **点击"科室管理"按钮**
4. **科室管理窗口将以对话框形式打开**

## 注意事项

1. **权限验证**：所有API调用都需要Bearer Token认证
2. **数据验证**：
   - 科室名称、地址不能为空
   - 挂号费必须是有效的数字
   - 病例名称不能为空，必须选择科别
3. **错误处理**：所有API调用都包含错误处理和用户友好的错误提示
4. **实时更新**：添加科室或病例后，左侧树形显示会自动刷新

## 技术实现

- **UI框架**：DevExpress WinForms控件
- **数据交互**：HttpClient + JSON序列化
- **异步处理**：async/await模式
- **树形控件**：DevExpress TreeList
- **布局管理**：Panel + GroupControl

## 扩展功能

页面预留了以下扩展功能的接口：
- 科室信息修改
- 病例信息删除
- 批量操作
- 数据导入导出
