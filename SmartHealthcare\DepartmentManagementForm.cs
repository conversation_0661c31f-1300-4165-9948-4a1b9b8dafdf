using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Newtonsoft.Json;

namespace SmartHealthcare
{
    public partial class DepartmentManagementForm : Form
    {
        private List<Department> departments = new List<Department>();
        private List<MedicalCase> medicalCases = new List<MedicalCase>();

        public DepartmentManagementForm()
        {
            InitializeComponent();
        }

        private async void DepartmentManagementForm_Load(object sender, EventArgs e)
        {
            // 初始化科室编码显示
            UpdateDepartmentCodeDisplay();

            await LoadDepartments();
            await LoadDepartmentComboBox();
        }

        private async Task LoadDepartments()
        {
            try
            {
                string url = APIURL.SReadURL + "api/SmartHealthcare/Handle";
                string response = await HttpClientHelper.ClientAsync("GET", url, true);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<Department>>>(response);
                    if (result.Success)
                    {
                        departments = result.Data;
                        LoadMedicalCasesFromDepartments(); // 从科室数据中提取病例
                        PopulateTreeList();
                    }
                    else
                    {
                        MessageBox.Show($"加载科室失败: {result.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"加载科室失败: {response}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载科室时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateTreeList()
        {
            treeViewDepartments.BeginUpdate();
            treeViewDepartments.Nodes.Clear();

            foreach (var department in departments)
            {
                TreeNode departmentNode = new TreeNode(department.DepartmentName);
                departmentNode.Tag = department;

                // 添加病例子节点
                foreach (var medicalCase in department.Cases)
                {
                    TreeNode caseNode = new TreeNode(medicalCase.CaseName);
                    caseNode.Tag = medicalCase;
                    departmentNode.Nodes.Add(caseNode);
                }

                treeViewDepartments.Nodes.Add(departmentNode);

                // 如果有病例，展开节点
                if (department.Cases.Count > 0)
                {
                    departmentNode.Expand();
                }
            }

            treeViewDepartments.EndUpdate();
        }

        private void treeViewDepartments_AfterSelect(object sender, TreeViewEventArgs e)
        {
            // 现在病例数据已经包含在科室数据中，不需要额外加载
            // 这个方法可以用于显示选中项的详细信息或其他交互
            if (e.Node != null)
            {
                if (e.Node.Tag is Department department)
                {
                    // 选中了科室节点
                    // 可以在这里添加显示科室详细信息的逻辑
                }
                else if (e.Node.Tag is MedicalCase medicalCase)
                {
                    // 选中了病例节点
                    // 可以在这里添加显示病例详细信息的逻辑
                }
            }
        }

        private void LoadMedicalCasesFromDepartments()
        {
            // 从科室数据中提取所有病例
            medicalCases.Clear();
            foreach (var department in departments)
            {
                medicalCases.AddRange(department.Cases);
            }
        }

        private async Task LoadDepartmentComboBox()
        {
            try
            {
                string url = APIURL.SReadURL + "api/SmartHealthcare/GetDepartment";
                string response = await HttpClientHelper.ClientAsync("GET", url, true);

                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<Department>>>(response);
                    if (result.Success)
                    {
                        comboBoxDepartment.Items.Clear();
                        comboBoxDepartment.DisplayMember = "Name";
                        comboBoxDepartment.ValueMember = "Id";

                        foreach (var dept in result.Data)
                        {
                            comboBoxDepartment.Items.Add(dept);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载科室下拉列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnAddDepartment_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtDepartmentName.Text) ||
                string.IsNullOrWhiteSpace(txtDepartmentAddress.Text) ||
                string.IsNullOrWhiteSpace(txtRegistrationFee.Text))
            {
                MessageBox.Show("请填写完整的科室信息", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!decimal.TryParse(txtRegistrationFee.Text, out decimal registrationFee))
            {
                MessageBox.Show("挂号费必须是有效的数字", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // 使用界面上显示的科室编码
                string departmentCode = lblDepartmentCode.Text;

                var departmentData = new
                {
                    DepartmentCode = departmentCode,
                    DepartmentName = txtDepartmentName.Text.Trim(),
                    Address = txtDepartmentAddress.Text.Trim(),
                    RegistrationMoney = registrationFee,
                    IsEmergency = 0  // 默认非急诊科室
                };

                string json = JsonConvert.SerializeObject(departmentData);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                
                string url = APIURL.SWriteURl + "api/SmartHealthcare/CreateDepartment";
                string response = await HttpClientHelper.ClientAsync("POST", url, true, content);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<object>>(response);
                    if (result.Success)
                    {
                        MessageBox.Show($"科室添加成功！\n科室编码：{departmentCode}\n科室名称：{txtDepartmentName.Text.Trim()}",
                            "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ClearDepartmentForm();
                        await LoadDepartments();
                        await LoadDepartmentComboBox();
                    }
                    else
                    {
                        MessageBox.Show($"添加科室失败: {result.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"添加科室失败: {response}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加科室时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClearDepartment_Click(object sender, EventArgs e)
        {
            ClearDepartmentForm();
        }

        private void ClearDepartmentForm()
        {
            txtDepartmentName.Text = "";
            txtDepartmentAddress.Text = "";
            txtRegistrationFee.Text = "";
            // 生成新的科室编码
            UpdateDepartmentCodeDisplay();
        }

        private async void btnAddCase_Click(object sender, EventArgs e)
        {
            if (comboBoxDepartment.SelectedItem == null || string.IsNullOrWhiteSpace(txtCaseName.Text))
            {
                MessageBox.Show("请选择科别并输入病例名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var department = (Department)comboBoxDepartment.SelectedItem;
                var caseData = new
                {
                    DepartmentId = department.Id,
                    Name = txtCaseName.Text.Trim()
                };

                string json = JsonConvert.SerializeObject(caseData);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                
                string url = APIURL.SWriteURl + "api/SmartHealthcare/CreateMedicalCase";
                string response = await HttpClientHelper.ClientAsync("POST", url, true, content);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<object>>(response);
                    if (result.Success)
                    {
                        MessageBox.Show("病例添加成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ClearCaseForm();
                        await LoadDepartments();
                    }
                    else
                    {
                        MessageBox.Show($"添加病例失败: {result.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"添加病例失败: {response}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加病例时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDeleteCase_Click(object sender, EventArgs e)
        {
            ClearCaseForm();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ClearCaseForm()
        {
            comboBoxDepartment.SelectedItem = null;
            txtCaseName.Text = "";
        }

        /// <summary>
        /// 生成科室编码：KS + 4位随机编码
        /// </summary>
        /// <returns>科室编码，格式：KS****</returns>
        private string GenerateDepartmentCode()
        {
            Random random = new Random();
            string randomCode = "";

            // 生成4位随机编码（数字和字母混合）
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            for (int i = 0; i < 4; i++)
            {
                randomCode += chars[random.Next(chars.Length)];
            }

            return "KS" + randomCode;
        }

        /// <summary>
        /// 更新科室编码显示
        /// </summary>
        private void UpdateDepartmentCodeDisplay()
        {
            string newCode = GenerateDepartmentCode();
            lblDepartmentCode.Text = newCode;
        }

        /// <summary>
        /// 重新生成科室编码按钮点击事件
        /// </summary>
        private void btnGenerateCode_Click(object sender, EventArgs e)
        {
            UpdateDepartmentCodeDisplay();
        }
    }

    // 数据模型类
    public class Department
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("departmentCode")]
        public string DepartmentCode { get; set; }

        [JsonProperty("departmentName")]
        public string DepartmentName { get; set; }

        [JsonProperty("address")]
        public string Address { get; set; }

        [JsonProperty("registrationMoney")]
        public decimal RegistrationMoney { get; set; }

        [JsonProperty("isEmergency")]
        public int IsEmergency { get; set; }

        [JsonProperty("cases")]
        public List<MedicalCase> Cases { get; set; } = new List<MedicalCase>();

        [JsonProperty("caseCount")]
        public int CaseCount { get; set; }

        // 为了兼容现有代码，添加这些属性
        public string Name
        {
            get => DepartmentName;
            set => DepartmentName = value;
        }

        public decimal RegistrationFee
        {
            get => RegistrationMoney;
            set => RegistrationMoney = value;
        }
    }

    public class MedicalCase
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("departmentId")]
        public int DepartmentId { get; set; }

        [JsonProperty("caseName")]
        public string CaseName { get; set; }

        [JsonProperty("createTime")]
        public DateTime CreateTime { get; set; }

        [JsonProperty("createUser")]
        public string CreateUser { get; set; }

        // 为了兼容现有代码，添加这个属性
        public string Name
        {
            get => CaseName;
            set => CaseName = value;
        }
    }

    public class ApiResponse<T>
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("msg")]
        public string Msg { get; set; }

        [JsonProperty("data")]
        public T Data { get; set; }

        // 为了兼容现有代码，添加这些属性
        public bool Success
        {
            get => Code == 200;
        }

        public string Message
        {
            get => Msg;
            set => Msg = value;
        }
    }
}
