﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace SmartHealthcare
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // 创建登录窗体
            var loginForm = new Form();
            loginForm.Text = "智慧医疗系统 - 登录";
            loginForm.Size = new Size(800, 600);  // 增大默认尺寸
            loginForm.MinimumSize = new Size(600, 400);  // 设置最小尺寸
            loginForm.StartPosition = FormStartPosition.CenterScreen;
            loginForm.FormBorderStyle = FormBorderStyle.Sizable;  // 允许调整大小
            loginForm.MaximizeBox = true;   // 允许最大化
            loginForm.MinimizeBox = true;   // 允许最小化
            
            // 添加登录控件
            var loginControl = new Login();
            loginControl.Dock = DockStyle.Fill;
            loginForm.Controls.Add(loginControl);
            
            Application.Run(loginForm);
        }
    }
}
