using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Mvc;
using System;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 添加CORS支持
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder =>
        {
            builder.AllowAnyOrigin()
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 注释掉HTTPS重定向，允许HTTP请求
// app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();
app.MapControllers();

app.Run();

// 登录响应模型
public class LoginResponse
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public string Token { get; set; }
    public string UserName { get; set; }
    public string RoleName { get; set; }
}

// 用户控制器
[ApiController]
[Route("api/[controller]")]
public class UserController : ControllerBase
{
    [HttpGet("Login")]
    public IActionResult Login(string userName, string password)
    {
        try
        {
            // 简单的用户验证逻辑
            if (userName == "admin" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "real_token_" + DateTime.Now.Ticks,
                    UserName = "管理员",
                    RoleName = "系统管理员"
                };
                return Ok(response);
            }
            else if (userName == "doctor" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "real_token_" + DateTime.Now.Ticks,
                    UserName = "张医生",
                    RoleName = "主治医师"
                };
                return Ok(response);
            }
            else
            {
                var response = new LoginResponse
                {
                    Success = false,
                    Message = "用户名或密码错误",
                    Token = null,
                    UserName = null,
                    RoleName = null
                };
                return Ok(response);
            }
        }
        catch (Exception ex)
        {
            var response = new LoginResponse
            {
                Success = false,
                Message = $"服务器错误：{ex.Message}",
                Token = null,
                UserName = null,
                RoleName = null
            };
            return StatusCode(500, response);
        }
    }

    // RBAC控制器
    [ApiController]
    [Route("api/[controller]")]
    public class RBACController : ControllerBase
    {
        [HttpGet("Login")]
        public IActionResult Login(string userName, string password)
        {
            try
            {
                // 模拟RBAC用户验证逻辑
                var users = new Dictionary<string, (string password, string displayName, string role)>
                {
                    { "admin", ("123456", "系统管理员", "Administrator") },
                    { "doctor", ("123456", "张医生", "Doctor") },
                    { "nurse", ("123456", "李护士", "Nurse") },
                    { "patient", ("123456", "王患者", "Patient") }
                };

                if (users.ContainsKey(userName.ToLower()) && users[userName.ToLower()].password == password)
                {
                    var userInfo = users[userName.ToLower()];
                    var response = new LoginResponse
                    {
                        Success = true,
                        Message = "登录成功",
                        Token = $"rbac_token_{DateTime.Now.Ticks}_{userName}",
                        UserName = userInfo.displayName,
                        RoleName = userInfo.role
                    };
                    return Ok(response);
                }
                else
                {
                    var response = new LoginResponse
                    {
                        Success = false,
                        Message = "用户名或密码错误，请检查后重试",
                        Token = null,
                        UserName = null,
                        RoleName = null
                    };
                    return Ok(response);
                }
            }
            catch (Exception ex)
            {
                var response = new LoginResponse
                {
                    Success = false,
                    Message = $"服务器内部错误：{ex.Message}",
                    Token = null,
                    UserName = null,
                    RoleName = null
                };
                return StatusCode(500, response);
            }
        }
    }
}

// RBAC控制器
[ApiController]
[Route("api/[controller]")]
public class RBACController : ControllerBase
{
    [HttpGet("Login")]
    public IActionResult Login(string userName, string password)
    {
        try
        {
            // 简单的用户验证逻辑
            if (userName == "admin" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "系统管理员",
                    RoleName = "超级管理员"
                };
                return Ok(response);
            }
            else if (userName == "doctor" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "张医生",
                    RoleName = "主治医师"
                };
                return Ok(response);
            }
            else if (userName == "nurse" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "李护士",
                    RoleName = "护士长"
                };
                return Ok(response);
            }
            else if (userName == "patient" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "王患者",
                    RoleName = "患者"
                };
                return Ok(response);
            }
            else
            {
                var response = new LoginResponse
                {
                    Success = false,
                    Message = "用户名或密码错误，请检查输入信息",
                    Token = null,
                    UserName = null,
                    RoleName = null
                };
                return Ok(response);
            }
        }
        catch (Exception ex)
        {
            var response = new LoginResponse
            {
                Success = false,
                Message = $"服务器内部错误：{ex.Message}",
                Token = null,
                UserName = null,
                RoleName = null
            };
            return StatusCode(500, response);
        }
    }
}

// SmartHealthcare控制器
[ApiController]
[Route("api/[controller]")]
public class SmartHealthcareController : ControllerBase
{
    // 模拟科室数据
    private static List<Department> departments = new List<Department>
    {
        new Department { Id = 1, Name = "内科", Address = "1楼东区", RegistrationMoney = 15.00m, Cases = new List<MedicalCase>() },
        new Department { Id = 2, Name = "外科", Address = "2楼西区", RegistrationMoney = 20.00m, Cases = new List<MedicalCase>() },
        new Department { Id = 3, Name = "儿科", Address = "3楼南区", RegistrationMoney = 18.00m, Cases = new List<MedicalCase>() }
    };

    private static List<MedicalCase> medicalCases = new List<MedicalCase>();
    private static int nextCaseId = 1;
    private static int nextDepartmentId = 4;

    [HttpGet("Handle")]
    public IActionResult GetDepartments()
    {
        try
        {
            var response = new ApiResponse<List<Department>>
            {
                Success = true,
                Message = "获取科室列表成功",
                Data = departments
            };
            return Ok(response);
        }
        catch (Exception ex)
        {
            var response = new ApiResponse<List<Department>>
            {
                Success = false,
                Message = $"获取科室列表失败：{ex.Message}",
                Data = null
            };
            return StatusCode(500, response);
        }
    }

    [HttpGet("GetDepartment")]
    public IActionResult GetDepartmentList()
    {
        try
        {
            var response = new ApiResponse<List<Department>>
            {
                Success = true,
                Message = "获取科室下拉列表成功",
                Data = departments
            };
            return Ok(response);
        }
        catch (Exception ex)
        {
            var response = new ApiResponse<List<Department>>
            {
                Success = false,
                Message = $"获取科室下拉列表失败：{ex.Message}",
                Data = null
            };
            return StatusCode(500, response);
        }
    }

    [HttpPost("CreateDepartment")]
    public IActionResult CreateDepartment([FromBody] CreateDepartmentRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrWhiteSpace(request.DepartmentName))
            {
                var errorResponse = new ApiResponse<object>
                {
                    Success = false,
                    Message = "科室名称不能为空",
                    Data = null
                };
                return BadRequest(errorResponse);
            }

            var newDepartment = new Department
            {
                Id = nextDepartmentId++,
                Name = request.DepartmentName,
                Address = request.Address ?? "",
                RegistrationMoney = request.RegistrationMoney,
                Cases = new List<MedicalCase>()
            };

            departments.Add(newDepartment);

            var response = new ApiResponse<object>
            {
                Success = true,
                Message = "科室创建成功",
                Data = new { Id = newDepartment.Id }
            };
            return Ok(response);
        }
        catch (Exception ex)
        {
            var response = new ApiResponse<object>
            {
                Success = false,
                Message = $"创建科室失败：{ex.Message}",
                Data = null
            };
            return StatusCode(500, response);
        }
    }

    [HttpPost("CreateMedicalCase")]
    public IActionResult CreateMedicalCase([FromBody] CreateMedicalCaseRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Name))
            {
                var errorResponse = new ApiResponse<object>
                {
                    Success = false,
                    Message = "病例名称不能为空",
                    Data = null
                };
                return BadRequest(errorResponse);
            }

            var department = departments.FirstOrDefault(d => d.Id == request.DepartmentId);
            if (department == null)
            {
                var errorResponse = new ApiResponse<object>
                {
                    Success = false,
                    Message = "指定的科室不存在",
                    Data = null
                };
                return BadRequest(errorResponse);
            }

            var newCase = new MedicalCase
            {
                Id = nextCaseId++,
                DepartmentId = request.DepartmentId,
                CaseName = request.Name,
                CreateTime = DateTime.Now,
                CreateUser = "当前用户" // 这里可以从Token中获取用户信息
            };

            medicalCases.Add(newCase);
            department.Cases.Add(newCase);

            var response = new ApiResponse<object>
            {
                Success = true,
                Message = "病例创建成功",
                Data = new { Id = newCase.Id }
            };
            return Ok(response);
        }
        catch (Exception ex)
        {
            var response = new ApiResponse<object>
            {
                Success = false,
                Message = $"创建病例失败：{ex.Message}",
                Data = null
            };
            return StatusCode(500, response);
        }
    }
}

// 数据模型
public class Department
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Address { get; set; }
    public decimal RegistrationMoney { get; set; }
    public List<MedicalCase> Cases { get; set; } = new List<MedicalCase>();
}

public class MedicalCase
{
    public int Id { get; set; }
    public int DepartmentId { get; set; }
    public string CaseName { get; set; }
    public DateTime CreateTime { get; set; }
    public string CreateUser { get; set; }
}

// 请求模型
public class CreateDepartmentRequest
{
    public string DepartmentCode { get; set; }
    public string DepartmentName { get; set; }
    public string Address { get; set; }
    public decimal RegistrationMoney { get; set; }
    public int IsEmergency { get; set; }
}

public class CreateMedicalCaseRequest
{
    public int DepartmentId { get; set; }
    public string Name { get; set; }
}

// API响应模型
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public T Data { get; set; }
}