using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Mvc;
using System;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 添加CORS支持
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder =>
        {
            builder.AllowAnyOrigin()
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 注释掉HTTPS重定向，允许HTTP请求
// app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();
app.MapControllers();

app.Run();

// 登录响应模型
public class LoginResponse
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public string Token { get; set; }
    public string UserName { get; set; }
    public string RoleName { get; set; }
}

// 用户控制器
[ApiController]
[Route("api/[controller]")]
public class UserController : ControllerBase
{
    [HttpGet("Login")]
    public IActionResult Login(string userName, string password)
    {
        try
        {
            // 简单的用户验证逻辑
            if (userName == "admin" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "real_token_" + DateTime.Now.Ticks,
                    UserName = "管理员",
                    RoleName = "系统管理员"
                };
                return Ok(response);
            }
            else if (userName == "doctor" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "real_token_" + DateTime.Now.Ticks,
                    UserName = "张医生",
                    RoleName = "主治医师"
                };
                return Ok(response);
            }
            else
            {
                var response = new LoginResponse
                {
                    Success = false,
                    Message = "用户名或密码错误",
                    Token = null,
                    UserName = null,
                    RoleName = null
                };
                return Ok(response);
            }
        }
        catch (Exception ex)
        {
            var response = new LoginResponse
            {
                Success = false,
                Message = $"服务器错误：{ex.Message}",
                Token = null,
                UserName = null,
                RoleName = null
            };
            return StatusCode(500, response);
        }
    }

    // RBAC控制器
    [ApiController]
    [Route("api/[controller]")]
    public class RBACController : ControllerBase
    {
        [HttpGet("Login")]
        public IActionResult Login(string userName, string password)
        {
            try
            {
                // 模拟RBAC用户验证逻辑
                var users = new Dictionary<string, (string password, string displayName, string role)>
                {
                    { "admin", ("123456", "系统管理员", "Administrator") },
                    { "doctor", ("123456", "张医生", "Doctor") },
                    { "nurse", ("123456", "李护士", "Nurse") },
                    { "patient", ("123456", "王患者", "Patient") }
                };

                if (users.ContainsKey(userName.ToLower()) && users[userName.ToLower()].password == password)
                {
                    var userInfo = users[userName.ToLower()];
                    var response = new LoginResponse
                    {
                        Success = true,
                        Message = "登录成功",
                        Token = $"rbac_token_{DateTime.Now.Ticks}_{userName}",
                        UserName = userInfo.displayName,
                        RoleName = userInfo.role
                    };
                    return Ok(response);
                }
                else
                {
                    var response = new LoginResponse
                    {
                        Success = false,
                        Message = "用户名或密码错误，请检查后重试",
                        Token = null,
                        UserName = null,
                        RoleName = null
                    };
                    return Ok(response);
                }
            }
            catch (Exception ex)
            {
                var response = new LoginResponse
                {
                    Success = false,
                    Message = $"服务器内部错误：{ex.Message}",
                    Token = null,
                    UserName = null,
                    RoleName = null
                };
                return StatusCode(500, response);
            }
        }
    }
}

// RBAC控制器
[ApiController]
[Route("api/[controller]")]
public class RBACController : ControllerBase
{
    [HttpGet("Login")]
    public IActionResult Login(string userName, string password)
    {
        try
        {
            // 简单的用户验证逻辑
            if (userName == "admin" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "系统管理员",
                    RoleName = "超级管理员"
                };
                return Ok(response);
            }
            else if (userName == "doctor" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "张医生",
                    RoleName = "主治医师"
                };
                return Ok(response);
            }
            else if (userName == "nurse" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "李护士",
                    RoleName = "护士长"
                };
                return Ok(response);
            }
            else if (userName == "patient" && password == "123456")
            {
                var response = new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = "rbac_token_" + DateTime.Now.Ticks,
                    UserName = "王患者",
                    RoleName = "患者"
                };
                return Ok(response);
            }
            else
            {
                var response = new LoginResponse
                {
                    Success = false,
                    Message = "用户名或密码错误，请检查输入信息",
                    Token = null,
                    UserName = null,
                    RoleName = null
                };
                return Ok(response);
            }
        }
        catch (Exception ex)
        {
            var response = new LoginResponse
            {
                Success = false,
                Message = $"服务器内部错误：{ex.Message}",
                Token = null,
                UserName = null,
                RoleName = null
            };
            return StatusCode(500, response);
        }
    }
}