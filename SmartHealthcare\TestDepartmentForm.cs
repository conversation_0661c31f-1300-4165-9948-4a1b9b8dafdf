using System;
using System.Windows.Forms;

namespace SmartHealthcare
{
    public partial class TestDepartmentForm : Form
    {
        public TestDepartmentForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.btnOpenDepartmentManagement = new System.Windows.Forms.Button();
            this.lblTitle = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // btnOpenDepartmentManagement
            // 
            this.btnOpenDepartmentManagement.Font = new System.Drawing.Font("Microsoft YaHei UI", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnOpenDepartmentManagement.Location = new System.Drawing.Point(150, 150);
            this.btnOpenDepartmentManagement.Name = "btnOpenDepartmentManagement";
            this.btnOpenDepartmentManagement.Size = new System.Drawing.Size(200, 50);
            this.btnOpenDepartmentManagement.TabIndex = 0;
            this.btnOpenDepartmentManagement.Text = "打开科室管理";
            this.btnOpenDepartmentManagement.UseVisualStyleBackColor = true;
            this.btnOpenDepartmentManagement.Click += new System.EventHandler(this.btnOpenDepartmentManagement_Click);
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new System.Drawing.Font("Microsoft YaHei UI", 16F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTitle.Location = new System.Drawing.Point(120, 80);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(260, 30);
            this.lblTitle.TabIndex = 1;
            this.lblTitle.Text = "科室管理页面测试";
            // 
            // TestDepartmentForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(500, 300);
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.btnOpenDepartmentManagement);
            this.Name = "TestDepartmentForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "科室管理测试";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private System.Windows.Forms.Button btnOpenDepartmentManagement;
        private System.Windows.Forms.Label lblTitle;

        private void btnOpenDepartmentManagement_Click(object sender, EventArgs e)
        {
            try
            {
                var departmentForm = new DepartmentManagementForm();
                departmentForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开科室管理页面时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
