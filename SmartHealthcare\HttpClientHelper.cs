﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http.Headers;
using System.Net.Http;

namespace SmartHealthcare
{
    public static class HttpClientHelper
    {
        /// <summary>
        /// Get/Post/Put/Delete(delete是通过url传值的方式)
        /// </summary>
        /// <param name="method">请求方式</param>
        /// <param name="url">请求地址</param>
        /// <param name="isAuthorzation">是否授权</param>
        /// <param name="content">参数内容</param>
        /// <returns></returns>
        public static async Task<string> ClientAsync(string method, string url, bool isAuthorzation = false, HttpContent content = null)
        {
            var result = "";
            try
           {
                using (HttpClient client = new HttpClient())
                {
                    // 设置超时时间
                    client.Timeout = TimeSpan.FromSeconds(30);
                    if (isAuthorzation)
                    {
                        AuthenticationHeaderValue authentication = new AuthenticationHeaderValue(
                        "Bearer",
                        TokenDto.Token);
                        client.DefaultRequestHeaders.Authorization = authentication;
                    }
                    HttpResponseMessage httpResponseMessage = null;

                    if (method.ToUpper() == "GET")
                    {
                        httpResponseMessage = await client.GetAsync(url);
                    }
                    else if (method.ToUpper() == "POST")
                    {
                        httpResponseMessage = await client.PostAsync(url, content);
                    }
                    else if (method.ToUpper() == "PUT")
                    {
                        httpResponseMessage = await client.PutAsync(url, content);
                    }
                    else if (method.ToUpper() == "DELETE")
                    {
                        httpResponseMessage = await client.DeleteAsync(url);
                    }
                    if (httpResponseMessage != null)
                    {
                        //判断请求是否成功
                        if (httpResponseMessage.IsSuccessStatusCode)
                        {
                            //json字符串获取
                            string json = await httpResponseMessage.Content.ReadAsStringAsync();
                            result = json;
                        }
                        else
                        {
                            // 处理HTTP错误状态码
                            var errorContent = await httpResponseMessage.Content.ReadAsStringAsync();
                            result = $"HTTP错误 {httpResponseMessage.StatusCode}: {errorContent}";
                        }
                    }
                    else
                    {
                        result = "请求失败";
                    }
                }
            }
            catch (HttpRequestException httpEx)
            {
                result = $"HTTP请求异常: {httpEx.Message}";
            }
            catch (TaskCanceledException tcEx)
            {
                if (tcEx.InnerException is TimeoutException)
                {
                    result = "HTTP请求超时";
                }
                else
                {
                    result = $"HTTP请求被取消: {tcEx.Message}";
                }
            }
            catch (Exception ex)
            {
                result = $"HTTP请求发生未知错误: {ex.Message}";
            }
            return result;
        }

        /// <summary>
        /// delete(通过body方式传值)
        /// </summary>
        /// <param name="url">请求地址</param>
        /// <param name="isAuthorzation">是否授权</param>
        /// <param name="content">参数内容</param>
        /// <returns></returns>
        public static async Task<string> DeleteAsync(string url, bool isAuthorzation = false, HttpContent content = null)
        {
            var result = "";
            using (HttpClient client = new HttpClient())
            {
                if (isAuthorzation)
                {
                    AuthenticationHeaderValue authentication = new AuthenticationHeaderValue(
                    "Bearer",
                    TokenDto.Token);
                    client.DefaultRequestHeaders.Authorization = authentication;
                }

                HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Delete, url);
                httpRequestMessage.Content = content;
                //发送请求
                HttpResponseMessage httpResponseMessage = await client.SendAsync(httpRequestMessage);
                //判断请求是否成功 
                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    //json字符串获取
                    string json = await httpResponseMessage.Content.ReadAsStringAsync();
                    result = json;
                }
            }
            return result;
        }
    }
}
