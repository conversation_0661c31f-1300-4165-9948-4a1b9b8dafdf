using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace SmartHealthcare
{
    /// <summary>
    /// JSON反序列化测试辅助类
    /// </summary>
    public static class JsonTestHelper
    {
        /// <summary>
        /// 测试实际返回的JSON是否能正确反序列化
        /// </summary>
        public static void TestJsonDeserialization()
        {
            // 您提供的实际JSON数据
            string testJson = @"{
                ""msg"": ""查询成功"",
                ""code"": 200,
                ""data"": [
                    {
                        ""id"": 1,
                        ""departmentCode"": ""ER001"",
                        ""departmentName"": ""儿科"",
                        ""address"": ""西楼102室"",
                        ""registrationMoney"": 25.00,
                        ""isEmergency"": 1,
                        ""cases"": [
                            {
                                ""id"": 1,
                                ""departmentId"": 1,
                                ""caseName"": ""小儿麻痹症"",
                                ""createTime"": ""2025-07-30T19:08:38.157329"",
                                ""createUser"": ""system""
                            },
                            {
                                ""id"": 2,
                                ""departmentId"": 1,
                                ""caseName"": ""手足口病"",
                                ""createTime"": ""2025-07-30T19:08:47.324485"",
                                ""createUser"": ""system""
                            }
                        ],
                        ""caseCount"": 2
                    },
                    {
                        ""id"": 2,
                        ""departmentCode"": ""Fu001"",
                        ""departmentName"": ""妇科"",
                        ""address"": ""西楼201室"",
                        ""registrationMoney"": 30.00,
                        ""isEmergency"": 1,
                        ""cases"": [],
                        ""caseCount"": 0
                    }
                ]
            }";

            try
            {
                Console.WriteLine("开始测试JSON反序列化...");
                
                // 反序列化测试
                var result = JsonConvert.DeserializeObject<ApiResponse<List<Department>>>(testJson);
                
                Console.WriteLine($"反序列化成功！");
                Console.WriteLine($"Code: {result.Code}");
                Console.WriteLine($"Msg: {result.Msg}");
                Console.WriteLine($"Success: {result.Success}");
                Console.WriteLine($"Message: {result.Message}");
                Console.WriteLine($"科室数量: {result.Data.Count}");
                
                foreach (var dept in result.Data)
                {
                    Console.WriteLine($"\n科室信息:");
                    Console.WriteLine($"  ID: {dept.Id}");
                    Console.WriteLine($"  代码: {dept.DepartmentCode}");
                    Console.WriteLine($"  名称: {dept.DepartmentName}");
                    Console.WriteLine($"  地址: {dept.Address}");
                    Console.WriteLine($"  挂号费: {dept.RegistrationMoney}");
                    Console.WriteLine($"  是否急诊: {dept.IsEmergency}");
                    Console.WriteLine($"  病例数量: {dept.CaseCount}");
                    Console.WriteLine($"  兼容属性 - Name: {dept.Name}");
                    Console.WriteLine($"  兼容属性 - RegistrationFee: {dept.RegistrationFee}");
                    
                    if (dept.Cases.Count > 0)
                    {
                        Console.WriteLine($"  病例列表:");
                        foreach (var case_ in dept.Cases)
                        {
                            Console.WriteLine($"    - ID: {case_.Id}, 名称: {case_.CaseName}, 科室ID: {case_.DepartmentId}");
                            Console.WriteLine($"      创建时间: {case_.CreateTime}, 创建用户: {case_.CreateUser}");
                            Console.WriteLine($"      兼容属性 - Name: {case_.Name}");
                        }
                    }
                }
                
                Console.WriteLine("\n✅ JSON反序列化测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ JSON反序列化测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试科室编码生成功能
        /// </summary>
        public static void TestDepartmentCodeGeneration()
        {
            try
            {
                var form = new DepartmentManagementForm();

                // 使用反射调用私有方法进行测试
                var method = typeof(DepartmentManagementForm).GetMethod("GenerateDepartmentCode",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (method != null)
                {
                    string message = "科室编码生成测试:\n\n";

                    for (int i = 0; i < 10; i++)
                    {
                        string code = (string)method.Invoke(form, null);
                        message += $"编码 {i + 1}: {code}\n";
                    }

                    message += "\n✅ 所有编码都以'KS'开头，后跟4位随机字符";

                    System.Windows.Forms.MessageBox.Show(message, "科室编码生成测试",
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
                }
                else
                {
                    System.Windows.Forms.MessageBox.Show("无法找到GenerateDepartmentCode方法", "测试失败",
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                }

                form.Dispose();
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"科室编码生成测试失败:\n{ex.Message}", "测试失败",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示测试结果对话框
        /// </summary>
        public static void ShowTestResult()
        {
            try
            {
                string testJson = @"{""msg"":""查询成功"",""code"":200,""data"":[{""id"":1,""departmentCode"":""ER001"",""departmentName"":""儿科"",""address"":""西楼102室"",""registrationMoney"":25.00,""isEmergency"":1,""cases"":[{""id"":1,""departmentId"":1,""caseName"":""小儿麻痹症"",""createTime"":""2025-07-30T19:08:38.157329"",""createUser"":""system""},{""id"":2,""departmentId"":1,""caseName"":""手足口病"",""createTime"":""2025-07-30T19:08:47.324485"",""createUser"":""system""}],""caseCount"":2},{""id"":2,""departmentCode"":""Fu001"",""departmentName"":""妇科"",""address"":""西楼201室"",""registrationMoney"":30.00,""isEmergency"":1,""cases"":[],""caseCount"":0}]}";
                
                var result = JsonConvert.DeserializeObject<ApiResponse<List<Department>>>(testJson);
                
                string message = $"JSON反序列化测试成功！\n\n";
                message += $"响应状态: {result.Code} - {result.Msg}\n";
                message += $"科室数量: {result.Data.Count}\n\n";
                
                foreach (var dept in result.Data)
                {
                    message += $"科室: {dept.DepartmentName} ({dept.DepartmentCode})\n";
                    message += $"地址: {dept.Address}\n";
                    message += $"挂号费: ¥{dept.RegistrationMoney}\n";
                    message += $"病例数量: {dept.CaseCount}\n";
                    
                    if (dept.Cases.Count > 0)
                    {
                        message += $"病例: ";
                        foreach (var case_ in dept.Cases)
                        {
                            message += $"{case_.CaseName}; ";
                        }
                        message += "\n";
                    }
                    message += "\n";
                }
                
                System.Windows.Forms.MessageBox.Show(message, "JSON反序列化测试结果", 
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"JSON反序列化测试失败:\n{ex.Message}", "测试失败", 
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 创建测试用的科室数据
        /// </summary>
        public static List<Department> CreateTestDepartments()
        {
            return new List<Department>
            {
                new Department
                {
                    Id = 1,
                    DepartmentCode = "ER001",
                    DepartmentName = "儿科",
                    Address = "西楼102室",
                    RegistrationMoney = 25.00m,
                    IsEmergency = 1,
                    CaseCount = 2,
                    Cases = new List<MedicalCase>
                    {
                        new MedicalCase
                        {
                            Id = 1,
                            DepartmentId = 1,
                            CaseName = "小儿麻痹症",
                            CreateTime = DateTime.Parse("2025-07-30T19:08:38.157329"),
                            CreateUser = "system"
                        },
                        new MedicalCase
                        {
                            Id = 2,
                            DepartmentId = 1,
                            CaseName = "手足口病",
                            CreateTime = DateTime.Parse("2025-07-30T19:08:47.324485"),
                            CreateUser = "system"
                        }
                    }
                },
                new Department
                {
                    Id = 2,
                    DepartmentCode = "Fu001",
                    DepartmentName = "妇科",
                    Address = "西楼201室",
                    RegistrationMoney = 30.00m,
                    IsEmergency = 1,
                    CaseCount = 0,
                    Cases = new List<MedicalCase>()
                }
            };
        }
    }
}
