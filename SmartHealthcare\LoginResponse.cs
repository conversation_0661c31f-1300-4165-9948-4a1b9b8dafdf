using System;
using Newtonsoft.Json;

namespace SmartHealthcare
{
    // 根据实际返回的JSON格式定义响应类
    public class LoginResponse
    {
        [JsonProperty("msg")]
        public string Msg { get; set; }

        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("data")]
        public UserData Data { get; set; }

        // 判断是否登录成功
        [JsonIgnore]
        public bool Success => Code == 200;

        // 获取消息
        [JsonIgnore]
        public string Message => Msg ?? "未知错误";

        // 获取Token
        [JsonIgnore]
        public string Token => Data?.Token ?? "";

        // 获取用户名
        [JsonIgnore]
        public string UserName => Data?.FullName ?? Data?.UserName ?? "未知用户";

        // 获取角色名
        [JsonIgnore]
        public string RoleName => Data?.RoleName ?? "未知角色";
    }

    // 用户数据类
    public class UserData
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("workNumber")]
        public string WorkNumber { get; set; }

        [JsonProperty("userName")]
        public string UserName { get; set; }

        [JsonProperty("fullName")]
        public string FullName { get; set; }

        [JsonProperty("sex")]
        public bool Sex { get; set; }

        [JsonProperty("address")]
        public string Address { get; set; }

        [JsonProperty("mobile")]
        public string Mobile { get; set; }

        [JsonProperty("renPing")]
        public string RenPing { get; set; }

        [JsonProperty("departmentId")]
        public int DepartmentId { get; set; }

        [JsonProperty("registrationFee")]
        public decimal RegistrationFee { get; set; }

        [JsonProperty("isEnable")]
        public bool IsEnable { get; set; }

        [JsonProperty("roleId")]
        public int RoleId { get; set; }

        [JsonProperty("roleName")]
        public string RoleName { get; set; }

        [JsonProperty("token")]
        public string Token { get; set; }
    }
}