﻿using DevExpress.XtraEditors;
using System;
using System.Drawing;
using System.Windows.Forms;
using SmartHealthcare;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Net;
using System.Web;

namespace SmartHealthcare
{
    public partial class Login : DevExpress.XtraEditors.XtraUserControl
    {
        public static UserInfo CurrentUser { get; set; }

        public Login()
        {
            InitializeComponent();
            InitializePasswordVisibility();
            InitializeResponsiveLayout();
        }

        private SimpleButton btnShowPassword; // 将按钮声明为类成员变量

        private void InitializePasswordVisibility()
        {
            // 设置密码框为密码模式
            this.textEditPassword1.Properties.UseSystemPasswordChar = true;

            // 添加查看密码按钮
            btnShowPassword = new SimpleButton();
            btnShowPassword.Text = "👁";
            btnShowPassword.Size = new Size(35, 30); // 稍微增大按钮尺寸
            btnShowPassword.Appearance.Font = new Font("Microsoft YaHei UI", 10F);
            btnShowPassword.Appearance.Options.UseFont = true;
            btnShowPassword.Appearance.BackColor = Color.LightGray;
            btnShowPassword.Appearance.Options.UseBackColor = true;
            btnShowPassword.Click += (s, e) =>
            {
                textEditPassword1.Properties.UseSystemPasswordChar = !textEditPassword1.Properties.UseSystemPasswordChar;
                btnShowPassword.Text = textEditPassword1.Properties.UseSystemPasswordChar ? "👁" : "🙈";
            };
            this.Controls.Add(btnShowPassword);
        }

        private void InitializeResponsiveLayout()
        {
            // 订阅控件大小变化事件
            this.SizeChanged += Login_SizeChanged;
            this.Load += Login_Load;

            // 设置背景色
            this.BackColor = Color.FromArgb(245, 245, 245); // 浅灰色背景

            // 美化输入框
            textEditUserName1.Properties.Appearance.BackColor = Color.White;
            textEditUserName1.Properties.Appearance.BorderColor = Color.FromArgb(200, 200, 200);
            textEditPassword1.Properties.Appearance.BackColor = Color.White;
            textEditPassword1.Properties.Appearance.BorderColor = Color.FromArgb(200, 200, 200);

            // 美化登录按钮
            simpleButton1.Appearance.BackColor = Color.FromArgb(0, 122, 204);
            simpleButton1.Appearance.ForeColor = Color.White;
            simpleButton1.Appearance.Options.UseBackColor = true;
            simpleButton1.Appearance.Options.UseForeColor = true;

            // 添加Enter键登录功能
            this.textEditUserName1.KeyPress += TextEdit_KeyPress;
            this.textEditPassword1.KeyPress += TextEdit_KeyPress;

            // 设置Tab顺序
            this.textEditUserName1.TabIndex = 0;
            this.textEditPassword1.TabIndex = 1;
            this.simpleButton1.TabIndex = 2;
        }

        private void TextEdit_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 按Enter键时触发登录
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true; // 阻止默认行为
                simpleButton1_Click(sender, EventArgs.Empty);
            }
        }



        private void Login_Load(object sender, EventArgs e)
        {
            // 页面加载时进行一次布局调整
            AdjustControlsLayout();
        }

        private void Login_SizeChanged(object sender, EventArgs e)
        {
            // 当控件大小变化时重新调整布局
            AdjustControlsLayout();
        }

        private void AdjustControlsLayout()
        {
            if (this.Width <= 0 || this.Height <= 0) return;

            // 根据窗体大小调整字体和控件尺寸
            AdjustFontSizes();
            AdjustControlSizes();

            // 计算控件的居中位置
            int centerX = this.Width / 2;
            int centerY = this.Height / 2;

            // 调整标题位置 - 在页面上方1/5处，增加一些上边距
            lblTitle.Location = new Point(
                centerX - lblTitle.Width / 2,
                this.Height / 5 - lblTitle.Height / 2
            );

            // 计算输入框宽度（响应式）
            int inputWidth = Math.Min(Math.Max(220, this.Width / 4), 320);
            int labelOffset = inputWidth / 2 + 60; // 增加标签与输入框的间距

            // 调整控件尺寸
            textEditUserName1.Width = inputWidth;
            textEditPassword1.Width = inputWidth;

            // 用户名区域 - 向上移动一些
            int userNameY = centerY - 80;
            label1.Location = new Point(
                centerX - labelOffset,
                userNameY
            );
            textEditUserName1.Location = new Point(
                centerX - inputWidth / 2,
                userNameY - 5
            );

            // 密码区域 - 增加与用户名的间距
            int passwordY = centerY - 20;
            label2.Location = new Point(
                centerX - labelOffset,
                passwordY
            );
            textEditPassword1.Location = new Point(
                centerX - inputWidth / 2,
                passwordY - 5
            );

            // 调整登录按钮位置 - 增加与密码框的间距
            simpleButton1.Location = new Point(
                centerX - simpleButton1.Width / 2,
                centerY + 80
            );

            // 重新调整密码显示按钮位置
            AdjustPasswordVisibilityButton();
        }

        private void AdjustFontSizes()
        {
            // 根据窗体大小调整字体
            float scaleFactor = Math.Min(this.Width / 800f, this.Height / 600f);
            scaleFactor = Math.Max(0.8f, Math.Min(scaleFactor, 1.5f)); // 限制缩放范围

            // 调整标题字体
            float titleFontSize = 24f * scaleFactor;
            lblTitle.Appearance.Font = new Font("Microsoft YaHei UI", titleFontSize, FontStyle.Bold);

            // 调整标签字体
            float labelFontSize = 12f * scaleFactor;
            label1.Font = new Font("Microsoft YaHei UI", labelFontSize);
            label2.Font = new Font("Microsoft YaHei UI", labelFontSize);

            // 调整按钮字体
            float buttonFontSize = 12f * scaleFactor;
            simpleButton1.Appearance.Font = new Font("Microsoft YaHei UI", buttonFontSize, FontStyle.Bold);
        }

        private void AdjustControlSizes()
        {
            // 根据窗体大小调整控件尺寸
            float scaleFactor = Math.Min(this.Width / 800f, this.Height / 600f);
            scaleFactor = Math.Max(0.8f, Math.Min(scaleFactor, 1.2f));

            // 调整输入框高度
            int inputHeight = (int)(32 * scaleFactor); // 稍微增加输入框高度
            textEditUserName1.Height = inputHeight;
            textEditPassword1.Height = inputHeight;

            // 调整登录按钮尺寸
            int buttonWidth = (int)(220 * scaleFactor); // 稍微增加按钮宽度
            int buttonHeight = (int)(45 * scaleFactor); // 稍微增加按钮高度
            simpleButton1.Size = new Size(buttonWidth, buttonHeight);

            // 调整密码显示按钮尺寸
            if (btnShowPassword != null)
            {
                int showPasswordButtonSize = (int)(32 * scaleFactor);
                btnShowPassword.Size = new Size(showPasswordButtonSize, showPasswordButtonSize);
            }
        }

        private void AdjustPasswordVisibilityButton()
        {
            if (btnShowPassword != null)
            {
                // 将按钮放置在密码框右侧，向右移动40%的距离
                int buttonOffsetX = (int)(textEditPassword1.Width * 0.4); // 向右移动40%
                int buttonX = textEditPassword1.Location.X + textEditPassword1.Width + buttonOffsetX;

                // 垂直居中对齐密码框
                int buttonY = textEditPassword1.Location.Y + (textEditPassword1.Height - btnShowPassword.Height) / 2;

                btnShowPassword.Location = new Point(buttonX, buttonY);
            }
        }

        private async void simpleButton1_Click(object sender, EventArgs e)
        {
            try
            {
                // 禁用登录按钮，防止重复点击
                this.simpleButton1.Enabled = false;
                this.simpleButton1.Text = "登录中...";

                var userName = this.textEditUserName1.Text?.Trim();
                var password = this.textEditPassword1.Text?.Trim();

                // 非空判断
                if (string.IsNullOrEmpty(userName))
                {
                    XtraMessageBox.Show("请输入用户名", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.textEditUserName1.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    XtraMessageBox.Show("请输入密码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.textEditPassword1.Focus();
                    return;
                }

                // 构建API请求URL
                var encodedUserName = HttpUtility.UrlEncode(userName);
                var encodedPassword = HttpUtility.UrlEncode(password);
                var url = APIURL.ReadURL + "api/RBAC/Login?userName=" + encodedUserName + "&password=" + encodedPassword;

                // 调用API
                var result = await HttpClientHelper.ClientAsync("GET", url, false, null);

                // 检查网络错误
                if (result.StartsWith("HTTP错误") || result.StartsWith("HTTP请求") || string.IsNullOrEmpty(result) || result == "请求失败")
                {
                    XtraMessageBox.Show("网络连接失败，请检查网络连接或API服务器状态", "连接错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 反序列化JSON响应
                var loginResponse = JsonConvert.DeserializeObject<LoginResponse>(result);

                if (loginResponse != null && loginResponse.Success)
                {
                    // 登录成功，保存用户信息
                    CurrentUser = new UserInfo
                    {
                        UserName = loginResponse.UserName,
                        RoleName = loginResponse.RoleName,
                        Token = loginResponse.Token,
                        LoginTime = DateTime.Now
                    };

                    // 保存Token
                    TokenDto.Token = loginResponse.Token;

                    // 显示成功状态
                    this.simpleButton1.Text = "登录成功";
                    await Task.Delay(500);

                    // 跳转到主页面
                    var form1 = new Form1();
                    form1.Show();
                    this.ParentForm?.Hide();
                }
                else
                {
                    // 登录失败
                    var errorMessage = loginResponse?.Message ?? "登录失败，请检查用户名和密码";
                    XtraMessageBox.Show(errorMessage, "登录失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // 清空密码框
                    this.textEditPassword1.Text = "";
                    this.textEditPassword1.Focus();
                }
            }
            catch (JsonException)
            {
                XtraMessageBox.Show("服务器返回数据格式错误，请联系系统管理员", "数据格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"登录过程中发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复登录按钮
                this.simpleButton1.Enabled = true;
                this.simpleButton1.Text = "登录";
            }
        }
                


    }
}
