# JSON反序列化问题解决方案

## 问题描述

在科室管理页面中，当调用API获取科室和病例数据时，出现JSON反序列化失败的问题。

**返回的JSON结构：**
```json
{
  "msg": "查询成功",
  "code": 200,
  "data": [
    {
      "id": 1,
      "departmentCode": "ER001",
      "departmentName": "儿科",
      "address": "西楼102室",
      "registrationMoney": 25.00,
      "isEmergency": 1,
      "cases": [
        {
          "id": 1,
          "departmentId": 1,
          "caseName": "小儿麻痹症",
          "createTime": "2025-07-30T19:08:38.157329",
          "createUser": "system"
        }
      ],
      "caseCount": 2
    }
  ]
}
```

**原始数据模型问题：**
- API响应结构不匹配（`Success`/`Message` vs `code`/`msg`）
- 科室属性名不匹配（`Name` vs `departmentName`）
- 病例数据嵌套在科室中，而不是单独获取

## 解决方案

### 1. 更新数据模型

#### ApiResponse 类
```csharp
public class ApiResponse<T>
{
    [JsonProperty("code")]
    public int Code { get; set; }
    
    [JsonProperty("msg")]
    public string Msg { get; set; }
    
    [JsonProperty("data")]
    public T Data { get; set; }

    // 兼容属性
    public bool Success => Code == 200;
    public string Message => Msg;
}
```

#### Department 类
```csharp
public class Department
{
    [JsonProperty("id")]
    public int Id { get; set; }
    
    [JsonProperty("departmentCode")]
    public string DepartmentCode { get; set; }
    
    [JsonProperty("departmentName")]
    public string DepartmentName { get; set; }
    
    [JsonProperty("address")]
    public string Address { get; set; }
    
    [JsonProperty("registrationMoney")]
    public decimal RegistrationMoney { get; set; }
    
    [JsonProperty("isEmergency")]
    public int IsEmergency { get; set; }
    
    [JsonProperty("cases")]
    public List<MedicalCase> Cases { get; set; } = new List<MedicalCase>();
    
    [JsonProperty("caseCount")]
    public int CaseCount { get; set; }

    // 兼容属性
    public string Name => DepartmentName;
    public decimal RegistrationFee => RegistrationMoney;
}
```

#### MedicalCase 类
```csharp
public class MedicalCase
{
    [JsonProperty("id")]
    public int Id { get; set; }
    
    [JsonProperty("departmentId")]
    public int DepartmentId { get; set; }
    
    [JsonProperty("caseName")]
    public string CaseName { get; set; }
    
    [JsonProperty("createTime")]
    public DateTime CreateTime { get; set; }
    
    [JsonProperty("createUser")]
    public string CreateUser { get; set; }

    // 兼容属性
    public string Name => CaseName;
}
```

### 2. 更新业务逻辑

#### 树形控件填充
```csharp
private void PopulateTreeList()
{
    treeViewDepartments.BeginUpdate();
    treeViewDepartments.Nodes.Clear();

    foreach (var department in departments)
    {
        TreeNode departmentNode = new TreeNode(department.DepartmentName);
        departmentNode.Tag = department;
        
        // 添加病例子节点
        foreach (var medicalCase in department.Cases)
        {
            TreeNode caseNode = new TreeNode(medicalCase.CaseName);
            caseNode.Tag = medicalCase;
            departmentNode.Nodes.Add(caseNode);
        }
        
        treeViewDepartments.Nodes.Add(departmentNode);
        
        // 如果有病例，展开节点
        if (department.Cases.Count > 0)
        {
            departmentNode.Expand();
        }
    }

    treeViewDepartments.EndUpdate();
}
```

#### 病例数据提取
```csharp
private void LoadMedicalCasesFromDepartments()
{
    // 从科室数据中提取所有病例
    medicalCases.Clear();
    foreach (var department in departments)
    {
        medicalCases.AddRange(department.Cases);
    }
}
```

### 3. 兼容性设计

为了保持与现有代码的兼容性，我们添加了兼容属性：

- `Department.Name` → `Department.DepartmentName`
- `Department.RegistrationFee` → `Department.RegistrationMoney`
- `MedicalCase.Name` → `MedicalCase.CaseName`
- `ApiResponse.Success` → `ApiResponse.Code == 200`
- `ApiResponse.Message` → `ApiResponse.Msg`

这样现有的代码可以继续使用原来的属性名，无需大量修改。

### 4. 测试工具

创建了 `JsonTestHelper` 类来测试JSON反序列化：

```csharp
// 控制台测试
JsonTestHelper.TestJsonDeserialization();

// 对话框测试
JsonTestHelper.ShowTestResult();
```

## 主要改进

### ✅ 解决的问题
1. **JSON反序列化成功** - 数据模型与API响应完全匹配
2. **病例数据正确显示** - 病例嵌套在科室数据中，一次性加载
3. **向后兼容** - 现有代码无需修改
4. **完整的测试工具** - 可以验证JSON反序列化是否正常

### ✅ 新增功能
1. **嵌套数据支持** - 科室包含病例列表
2. **自动展开** - 有病例的科室自动展开显示
3. **测试工具** - 演示程序中添加"测试JSON"按钮
4. **详细属性** - 支持科室代码、急诊标识等新字段

## 使用方法

### 1. 测试JSON反序列化
```csharp
// 在演示程序中点击"测试JSON"按钮
DepartmentManagementDemo.ShowDemo();

// 或者直接调用
JsonTestHelper.ShowTestResult();
```

### 2. 正常使用科室管理页面
现在页面应该能够正确加载和显示科室及病例数据了。

### 3. 验证数据加载
- 左侧树形控件应该显示科室列表
- 点击科室可以看到展开的病例列表
- 科室和病例信息应该完整显示

## 注意事项

1. **API响应格式** - 确保后台API返回的JSON格式与我们的模型匹配
2. **日期格式** - `createTime` 字段使用ISO 8601格式
3. **数字精度** - `registrationMoney` 使用decimal类型保证精度
4. **空数据处理** - 妥善处理空的病例列表

## 故障排除

如果仍然出现反序列化问题：

1. **检查JSON格式** - 使用JsonTestHelper验证
2. **查看错误信息** - 检查具体的异常详情
3. **验证属性映射** - 确保JsonProperty标签正确
4. **测试网络连接** - 确保API服务器正常响应

现在科室管理页面应该能够正确处理您提供的JSON数据格式了！
