using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartHealthcare
{
    /// <summary>
    /// 科室管理页面测试类
    /// 用于测试科室管理页面的各项功能
    /// </summary>
    public static class DepartmentManagementTest
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("开始科室管理页面功能测试...");
            
            try
            {
                await TestApiConnection();
                TestFormCreation();
                TestDataValidation();
                TestUIComponents();
                
                Console.WriteLine("所有测试完成！");
                MessageBox.Show("科室管理页面功能测试完成！\n请查看控制台输出了解详细结果。", 
                    "测试完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                MessageBox.Show($"测试过程中发生错误:\n{ex.Message}", 
                    "测试错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 测试API连接
        /// </summary>
        private static async Task TestApiConnection()
        {
            Console.WriteLine("\n=== 测试API连接 ===");
            
            try
            {
                string url = APIURL.ReadURL + "api/SmartHealthcare/Handle";
                string response = await HttpClientHelper.ClientAsync("GET", url, false);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    Console.WriteLine("✓ API连接测试通过");
                }
                else
                {
                    Console.WriteLine($"✗ API连接测试失败: {response}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ API连接测试异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试窗体创建
        /// </summary>
        private static void TestFormCreation()
        {
            Console.WriteLine("\n=== 测试窗体创建 ===");
            
            try
            {
                var form = new DepartmentManagementForm();
                Console.WriteLine("✓ 科室管理窗体创建成功");
                
                // 测试窗体属性
                if (form.Text == "科室科别增加")
                {
                    Console.WriteLine("✓ 窗体标题设置正确");
                }
                else
                {
                    Console.WriteLine("✗ 窗体标题设置错误");
                }
                
                form.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 窗体创建失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试数据验证
        /// </summary>
        private static void TestDataValidation()
        {
            Console.WriteLine("\n=== 测试数据验证 ===");
            
            // 测试科室数据模型
            try
            {
                var department = new Department
                {
                    Id = 1,
                    Name = "内科",
                    Address = "1号楼2层",
                    RegistrationFee = 15.00m
                };
                
                Console.WriteLine("✓ 科室数据模型创建成功");
                
                if (!string.IsNullOrEmpty(department.Name))
                {
                    Console.WriteLine("✓ 科室名称验证通过");
                }
                
                if (department.RegistrationFee > 0)
                {
                    Console.WriteLine("✓ 挂号费验证通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 数据验证测试失败: {ex.Message}");
            }
            
            // 测试病例数据模型
            try
            {
                var medicalCase = new MedicalCase
                {
                    Id = 1,
                    Name = "感冒",
                    DepartmentId = 1
                };
                
                Console.WriteLine("✓ 病例数据模型创建成功");
                
                if (!string.IsNullOrEmpty(medicalCase.Name))
                {
                    Console.WriteLine("✓ 病例名称验证通过");
                }
                
                if (medicalCase.DepartmentId > 0)
                {
                    Console.WriteLine("✓ 科室ID验证通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 病例数据验证测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试UI组件
        /// </summary>
        private static void TestUIComponents()
        {
            Console.WriteLine("\n=== 测试UI组件 ===");
            
            try
            {
                var form = new DepartmentManagementForm();
                
                // 检查主要控件是否存在
                bool hasTreeView = false;
                bool hasDepartmentGroup = false;
                bool hasCaseGroup = false;
                
                foreach (Control control in form.Controls)
                {
                    if (control is Panel mainPanel)
                    {
                        foreach (Control subControl in mainPanel.Controls)
                        {
                            if (subControl is Panel leftPanel)
                            {
                                foreach (Control leftSubControl in leftPanel.Controls)
                                {
                                    if (leftSubControl is TreeView)
                                    {
                                        hasTreeView = true;
                                        break;
                                    }
                                }
                            }
                            else if (subControl is Panel rightPanel)
                            {
                                foreach (Control rightSubControl in rightPanel.Controls)
                                {
                                    if (rightSubControl is GroupBox groupBox)
                                    {
                                        if (groupBox.Text.Contains("科别"))
                                        {
                                            hasDepartmentGroup = true;
                                        }
                                        else if (groupBox.Text.Contains("病例"))
                                        {
                                            hasCaseGroup = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                if (hasTreeView)
                {
                    Console.WriteLine("✓ TreeView控件存在");
                }
                else
                {
                    Console.WriteLine("✗ TreeView控件缺失");
                }
                
                if (hasDepartmentGroup)
                {
                    Console.WriteLine("✓ 科室管理GroupBox存在");
                }
                else
                {
                    Console.WriteLine("✗ 科室管理GroupBox缺失");
                }
                
                if (hasCaseGroup)
                {
                    Console.WriteLine("✓ 病例管理GroupBox存在");
                }
                else
                {
                    Console.WriteLine("✗ 病例管理GroupBox缺失");
                }
                
                form.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ UI组件测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建测试数据
        /// </summary>
        public static List<Department> CreateTestDepartments()
        {
            return new List<Department>
            {
                new Department { Id = 1, Name = "内科", Address = "1号楼2层", RegistrationFee = 15.00m },
                new Department { Id = 2, Name = "外科", Address = "2号楼1层", RegistrationFee = 20.00m },
                new Department { Id = 3, Name = "儿科", Address = "3号楼3层", RegistrationFee = 18.00m },
                new Department { Id = 4, Name = "妇科", Address = "4号楼2层", RegistrationFee = 25.00m },
                new Department { Id = 5, Name = "眼科", Address = "5号楼1层", RegistrationFee = 30.00m }
            };
        }

        /// <summary>
        /// 创建测试病例数据
        /// </summary>
        public static List<MedicalCase> CreateTestMedicalCases()
        {
            return new List<MedicalCase>
            {
                new MedicalCase { Id = 1, Name = "感冒", DepartmentId = 1 },
                new MedicalCase { Id = 2, Name = "发烧", DepartmentId = 1 },
                new MedicalCase { Id = 3, Name = "阑尾炎", DepartmentId = 2 },
                new MedicalCase { Id = 4, Name = "骨折", DepartmentId = 2 },
                new MedicalCase { Id = 5, Name = "小儿肺炎", DepartmentId = 3 },
                new MedicalCase { Id = 6, Name = "妇科炎症", DepartmentId = 4 },
                new MedicalCase { Id = 7, Name = "近视", DepartmentId = 5 }
            };
        }

        /// <summary>
        /// 显示测试菜单
        /// </summary>
        public static void ShowTestMenu()
        {
            var testForm = new Form
            {
                Text = "科室管理页面测试菜单",
                Size = new System.Drawing.Size(400, 300),
                StartPosition = FormStartPosition.CenterScreen
            };

            var runTestButton = new Button
            {
                Text = "运行所有测试",
                Size = new System.Drawing.Size(150, 40),
                Location = new System.Drawing.Point(125, 50)
            };

            var openFormButton = new Button
            {
                Text = "打开科室管理页面",
                Size = new System.Drawing.Size(150, 40),
                Location = new System.Drawing.Point(125, 100)
            };

            var showDataButton = new Button
            {
                Text = "显示测试数据",
                Size = new System.Drawing.Size(150, 40),
                Location = new System.Drawing.Point(125, 150)
            };

            runTestButton.Click += async (s, e) => await RunAllTests();
            
            openFormButton.Click += (s, e) =>
            {
                var departmentForm = new DepartmentManagementForm();
                departmentForm.ShowDialog();
            };

            showDataButton.Click += (s, e) =>
            {
                var departments = CreateTestDepartments();
                var cases = CreateTestMedicalCases();
                
                string data = "测试科室数据:\n";
                foreach (var dept in departments)
                {
                    data += $"- {dept.Name} ({dept.Address}, ¥{dept.RegistrationFee})\n";
                }
                
                data += "\n测试病例数据:\n";
                foreach (var case_ in cases)
                {
                    data += $"- {case_.Name} (科室ID: {case_.DepartmentId})\n";
                }
                
                MessageBox.Show(data, "测试数据", MessageBoxButtons.OK, MessageBoxIcon.Information);
            };

            testForm.Controls.Add(runTestButton);
            testForm.Controls.Add(openFormButton);
            testForm.Controls.Add(showDataButton);

            testForm.ShowDialog();
        }
    }
}
