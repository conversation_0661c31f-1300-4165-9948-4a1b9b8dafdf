using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace SmartHealthcare
{
    public partial class HospitalizationFormDesigner : Form
    {
        private List<string> selectedItems = new List<string>();
        private List<CheckBox> printItemCheckBoxes = new List<CheckBox>();

        public HospitalizationFormDesigner()
        {
            InitializeComponent();
            InitializePrintItems();
        }

        private void InitializePrintItems()
        {
            // 初始化住院单打印项目复选框
            string[] printItems = {
                "住院号", "病房号", "床位号", "入院日期", "出院日期", 
                "主治医师", "护理等级", "诊断结果", "治疗方案", "住院费用", 
                "陪护人员", "紧急联系人", "医保类型", "预交金额"
            };

            int startY = 130;
            for (int i = 0; i < printItems.Length; i++)
            {
                var checkBox = new CheckBox();
                checkBox.Text = printItems[i];
                checkBox.Location = new Point(340, startY + i * 25);
                checkBox.Size = new Size(80, 20);
                checkBox.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
                checkBox.CheckedChanged += PrintItem_CheckedChanged;
                
                printItemCheckBoxes.Add(checkBox);
                this.Controls.Add(checkBox);
            }
        }

        private void PrintItem_CheckedChanged(object sender, EventArgs e)
        {
            var checkBox = sender as CheckBox;
            if (checkBox.Checked)
            {
                if (!selectedItems.Contains(checkBox.Text))
                {
                    selectedItems.Add(checkBox.Text);
                }
            }
            else
            {
                selectedItems.Remove(checkBox.Text);
            }
            UpdateDesignArea();
        }

        private void UpdateDesignArea()
        {
            // 清空设计区
            designArea.Controls.Clear();

            // 添加选中的项目到设计区
            int yPosition = 10;
            foreach (string item in selectedItems)
            {
                var label = new Label();
                label.Text = item;
                label.Location = new Point(10, yPosition);
                label.Size = new Size(200, 20);
                label.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
                label.ForeColor = Color.Black;
                
                designArea.Controls.Add(label);
                yPosition += 25;
            }
        }

        private void btnAddItem_Click(object sender, EventArgs e)
        {
            string customItem = txtCustomItem.Text.Trim();
            if (!string.IsNullOrEmpty(customItem) && !selectedItems.Contains(customItem))
            {
                selectedItems.Add(customItem);
                UpdateDesignArea();
                txtCustomItem.Clear();
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            // 取消所有复选框选中状态
            foreach (var checkBox in printItemCheckBoxes)
            {
                checkBox.Checked = false;
            }
            
            // 清空选中项目列表
            selectedItems.Clear();
            
            // 更新设计区
            UpdateDesignArea();
        }

        private void btnPreview_Click(object sender, EventArgs e)
        {
            // 清空预览区
            previewArea.Controls.Clear();

            // 添加住院单标题
            var titleLabel = new Label();
            titleLabel.Text = "住院单";
            titleLabel.Font = new System.Drawing.Font("Microsoft YaHei UI", 16F, FontStyle.Bold);
            titleLabel.ForeColor = Color.Blue;
            titleLabel.Size = new Size(100, 30);
            titleLabel.Location = new Point((previewArea.Width - 100) / 2, 10);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            previewArea.Controls.Add(titleLabel);

            // 添加设计区的内容到预览区
            int yPosition = 50;
            foreach (string item in selectedItems)
            {
                var label = new Label();
                label.Text = item + ": _______________";
                label.Location = new Point(20, yPosition);
                label.Size = new Size(200, 20);
                label.Font = new System.Drawing.Font("Microsoft YaHei UI", 10F);
                label.ForeColor = Color.Black;
                
                previewArea.Controls.Add(label);
                yPosition += 25;
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // 生成打印内容
                string printContent = "住院单\n\n";
                foreach (string item in selectedItems)
                {
                    printContent += item + ": _______________\n";
                }
                
                // 显示打印预览
                DialogResult result = XtraMessageBox.Show("打印内容:\n\n" + printContent + "\n\n确定要打印到文件吗？", 
                    "打印预览", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    PrintToFile(printContent);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("打印失败: " + ex.Message, "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintToFile(string content)
        {
            try
            {
                // 选择保存文件夹
                FolderBrowserDialog folderDialog = new FolderBrowserDialog();
                folderDialog.Description = "选择保存住院单文件的文件夹";
                folderDialog.ShowNewFolderButton = true;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    string basePath = Path.Combine(folderDialog.SelectedPath, "住院单_" + timestamp);
                    
                    // 保存TXT文件
                    string txtFileName = basePath + ".txt";
                    string formattedContent = FormatPrintContent(content);
                    File.WriteAllText(txtFileName, formattedContent, System.Text.Encoding.UTF8);
                    
                    // 保存图片文件
                    string imageFileName = basePath + ".png";
                    SavePreviewAsImage(imageFileName);
                    
                    XtraMessageBox.Show($"住院单已成功保存:\n文本文件: {txtFileName}\n图片文件: {imageFileName}", 
                        "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // 询问是否打开文件夹
                    DialogResult openResult = XtraMessageBox.Show("是否要打开保存的文件夹？", "提示", 
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (openResult == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start("explorer.exe", folderDialog.SelectedPath);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("保存文件失败: " + ex.Message, "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SavePreviewAsImage(string fileName)
        {
            try
            {
                // 创建一个位图来绘制预览内容
                int width = 600;
                int height = 800;
                using (Bitmap bitmap = new Bitmap(width, height))
                using (Graphics graphics = Graphics.FromImage(bitmap))
                {
                    // 设置背景色为白色
                    graphics.Clear(Color.White);
                    graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                    graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

                    // 绘制标题
                    using (System.Drawing.Font titleFont = new System.Drawing.Font("Microsoft YaHei UI", 24, FontStyle.Bold))
                    using (SolidBrush titleBrush = new SolidBrush(Color.Blue))
                    {
                        string title = "住院单";
                        SizeF titleSize = graphics.MeasureString(title, titleFont);
                        float titleX = (width - titleSize.Width) / 2;
                        graphics.DrawString(title, titleFont, titleBrush, titleX, 50);
                    }

                    // 绘制分隔线
                    using (Pen pen = new Pen(Color.Black, 2))
                    {
                        graphics.DrawLine(pen, 50, 120, width - 50, 120);
                    }

                    // 绘制内容
                    using (System.Drawing.Font contentFont = new System.Drawing.Font("Microsoft YaHei UI", 14))
                    using (SolidBrush contentBrush = new SolidBrush(Color.Black))
                    {
                        float yPosition = 160;
                        foreach (string item in selectedItems)
                        {
                            string text = item + ": _______________";
                            graphics.DrawString(text, contentFont, contentBrush, 80, yPosition);
                            yPosition += 40;
                        }
                    }

                    // 绘制底部信息
                    using (System.Drawing.Font footerFont = new System.Drawing.Font("Microsoft YaHei UI", 10))
                    using (SolidBrush footerBrush = new SolidBrush(Color.Gray))
                    {
                        string footer = "打印时间: " + DateTime.Now.ToString("yyyy年MM月dd日 HH:mm:ss");
                        graphics.DrawString(footer, footerFont, footerBrush, 80, height - 100);
                        
                        string hospital = "医院名称: 智慧医疗系统";
                        graphics.DrawString(hospital, footerFont, footerBrush, 80, height - 70);
                    }

                    // 保存图片
                    bitmap.Save(fileName, ImageFormat.Png);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("保存图片失败: " + ex.Message);
            }
        }

        private string FormatPrintContent(string content)
        {
            string formattedContent = "";
            formattedContent += "================================\n";
            formattedContent += "           住院单\n";
            formattedContent += "================================\n\n";
            formattedContent += "打印时间: " + DateTime.Now.ToString("yyyy年MM月dd日 HH:mm:ss") + "\n\n";
            
            foreach (string item in selectedItems)
            {
                formattedContent += item.PadRight(12, '　') + ": _______________\n\n";
            }
            
            formattedContent += "\n================================\n";
            formattedContent += "医院名称: 智慧医疗系统\n";
            formattedContent += "================================\n";
            
            return formattedContent;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // 保存预览内容为Word文档
                SavePreviewAsWord();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("保存失败: " + ex.Message, "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SavePreviewAsWord()
        {
            SaveFileDialog saveDialog = new SaveFileDialog();
            saveDialog.Filter = "Word文档|*.doc|RTF文档|*.rtf|所有文件|*.*";
            saveDialog.Title = "保存预览为Word文档";
            saveDialog.FileName = "住院单预览_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".rtf";
            saveDialog.DefaultExt = "rtf";

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                // 创建RTF格式的Word文档内容
                StringBuilder rtfContent = new StringBuilder();
                rtfContent.AppendLine(@"{\rtf1\ansi\deff0");
                rtfContent.AppendLine(@"{\fonttbl{\f0 Microsoft YaHei UI;}}");
                rtfContent.AppendLine(@"{\colortbl;\red0\green0\blue255;\red0\green0\blue0;}");
                
                // 添加标题
                rtfContent.AppendLine(@"\qc\f0\fs32\cf1\b 住院单\b0\par");
                rtfContent.AppendLine(@"\par");
                
                // 添加内容
                rtfContent.AppendLine(@"\ql\fs24\cf2");
                foreach (string item in selectedItems)
                {
                    rtfContent.AppendLine($@"{item}: _______________\par");
                    rtfContent.AppendLine(@"\par");
                }
                
                rtfContent.AppendLine(@"}");
                
                File.WriteAllText(saveDialog.FileName, rtfContent.ToString(), Encoding.UTF8);
                
                XtraMessageBox.Show("预览已成功保存为Word文档:\n" + saveDialog.FileName, "保存成功", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // 询问是否打开文件
                DialogResult openResult = XtraMessageBox.Show("是否要打开保存的Word文档？", "提示", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (openResult == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start(saveDialog.FileName);
                }
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void HospitalizationFormDesigner_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化
        }
    }
}
