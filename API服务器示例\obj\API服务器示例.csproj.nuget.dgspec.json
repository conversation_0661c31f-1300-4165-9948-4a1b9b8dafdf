{"format": 1, "restore": {"D:\\实训\\VUE\\SmartHealthcare\\API服务器示例\\API服务器示例.csproj": {}}, "projects": {"D:\\实训\\VUE\\SmartHealthcare\\API服务器示例\\API服务器示例.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训\\VUE\\SmartHealthcare\\API服务器示例\\API服务器示例.csproj", "projectName": "API服务器示例", "projectPath": "D:\\实训\\VUE\\SmartHealthcare\\API服务器示例\\API服务器示例.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训\\VUE\\SmartHealthcare\\API服务器示例\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DEv1\\Components\\Offline Packages", "D:\\新建文件夹 (2)\\333\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "E:\\DEv1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.2.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}