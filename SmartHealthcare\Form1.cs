using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using SmartHealthcare;

namespace SmartHealthcare
{
    public partial class Form1 : Form
    {
        private Timer timeTimer;

        public Form1()
        {
            InitializeComponent();
            InitializeTimer();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            // 显示用户信息
            DisplayUserInfo();

            // 更新日期时间
            UpdateDateTime();
        }

        private void InitializeTimer()
        {
            // 创建定时器更新时间
            timeTimer = new Timer();
            timeTimer.Interval = 1000; // 每秒更新一次
            timeTimer.Tick += TimeTimer_Tick;
            timeTimer.Start();
        }

        private void TimeTimer_Tick(object sender, EventArgs e)
        {
            UpdateDateTime();
        }

        private void UpdateDateTime()
        {
            DateTime now = DateTime.Now;
            string[] weekDays = { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
            string weekDay = weekDays[(int)now.DayOfWeek];
            lblDateTime.Text = $"{now:HH:mm:ss}  {now:yyyy年MM月dd日} {weekDay}";
        }

        private void DisplayUserInfo()
        {
            if (Login.CurrentUser != null)
            {
                lblUserInfo.Text = $"当前用户：{Login.CurrentUser.UserName}";
            }
            else
            {
                lblUserInfo.Text = "当前用户：未知";
            }
        }

        // 导航按钮点击事件
        private void btnSystemManagement_Click(object sender, EventArgs e)
        {
            ShowPage("系统管理页面");
        }

        private void btnDoorDoctor_Click(object sender, EventArgs e)
        {
            ShowPage("门诊医生页面");
        }

        private void btnDoorManagement_Click(object sender, EventArgs e)
        {
            ShowPage("门诊管理页面");
        }

        private void btnDoorCharge_Click(object sender, EventArgs e)
        {
            ShowPage("门诊收费页面");
        }

        private void btnHospitalManagement_Click(object sender, EventArgs e)
        {
            ShowPage("住院管理页面");
        }

        private void btnDrugManagement_Click(object sender, EventArgs e)
        {
            ShowPage("药房管理页面");
        }

        private void btnDrugStorage_Click(object sender, EventArgs e)
        {
            ShowPage("药库管理页面");
        }

        private void btnFinanceManagement_Click(object sender, EventArgs e)
        {
            ShowPage("财务管理页面");
        }



        private void ShowPage(string pageName)
        {
            // 清空内容面板
            panelContent.Controls.Clear();

            // 根据页面名称创建不同的页面内容
            switch (pageName)
            {
                case "系统管理页面":
                    CreateSystemManagementPage();
                    break;
                case "门诊医生页面":
                    CreateDoorDoctorPage();
                    break;
                case "门诊管理页面":
                    CreateDoorManagementPage();
                    break;
                case "门诊收费页面":
                    CreateDoorChargePage();
                    break;
                case "住院管理页面":
                    CreateHospitalManagementPage();
                    break;
                case "药房管理页面":
                    CreateDrugManagementPage();
                    break;
                case "药库管理页面":
                    CreateDrugStoragePage();
                    break;
                case "财务管理页面":
                    CreateFinanceManagementPage();
                    break;
                default:
                    CreateDefaultPage(pageName);
                    break;
            }
        }

        private void CreateDefaultPage(string pageName)
        {
            // 创建页面标题
            var pageLabel = new LabelControl();
            pageLabel.Text = $"当前是{pageName}";
            pageLabel.Appearance.Font = new Font("Microsoft YaHei UI", 28F, FontStyle.Bold);
            pageLabel.Appearance.ForeColor = Color.FromArgb(64, 64, 64);
            pageLabel.Appearance.Options.UseFont = true;
            pageLabel.Appearance.Options.UseForeColor = true;
            pageLabel.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            pageLabel.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 居中显示
            pageLabel.Location = new Point(
                (panelContent.Width - 400) / 2,
                (panelContent.Height - 60) / 2
            );
            pageLabel.Size = new Size(400, 60);
            pageLabel.Anchor = AnchorStyles.None;

            panelContent.Controls.Add(pageLabel);
        }

        private void CreateSystemManagementPage()
        {
            // 创建页面标题
            var titleLabel = new LabelControl();
            titleLabel.Text = "系统管理";
            titleLabel.Appearance.Font = new Font("Microsoft YaHei UI", 28F, FontStyle.Bold);
            titleLabel.Appearance.ForeColor = Color.FromArgb(54, 78, 111);
            titleLabel.Appearance.Options.UseFont = true;
            titleLabel.Appearance.Options.UseForeColor = true;
            titleLabel.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            titleLabel.AutoSizeMode = LabelAutoSizeMode.None;
            titleLabel.Size = new Size(400, 40);
            titleLabel.Location = new Point((panelContent.Width - 400) / 2, 50);
            titleLabel.Anchor = AnchorStyles.Top;
            panelContent.Controls.Add(titleLabel);

            // 创建功能按钮网格 (2行4列)
            string[] functionNames = {
                "挂号单设置", "门诊单设置", "住院单设置", "数据库备份",
                "科室管理", "权限管理", "员工添加", "数据恢复"
            };

            // 计算按钮布局
            int buttonWidth = 120;
            int buttonHeight = 100;
            int horizontalSpacing = 150;
            int verticalSpacing = 120;
            int startX = (panelContent.Width - (4 * buttonWidth + 3 * horizontalSpacing)) / 2;
            int startY = 150;

            for (int i = 0; i < functionNames.Length; i++)
            {
                int row = i / 4;
                int col = i % 4;

                CreateSystemManagementButton(
                    functionNames[i],
                    startX + col * (buttonWidth + horizontalSpacing),
                    startY + row * (buttonHeight + verticalSpacing),
                    buttonWidth,
                    buttonHeight
                );
            }
        }

        private void CreateSystemManagementButton(string functionName, int x, int y, int width, int height)
        {
            // 创建按钮容器面板
            var buttonPanel = new Panel();
            buttonPanel.Location = new Point(x, y);
            buttonPanel.Size = new Size(width, height);
            buttonPanel.BackColor = Color.Transparent;
            buttonPanel.Cursor = Cursors.Hand;

            // 创建圆形图标背景
            var iconPanel = new Panel();
            iconPanel.Size = new Size(60, 60);
            iconPanel.Location = new Point((width - 60) / 2, 10);
            iconPanel.BackColor = GetIconColor(functionName);
            iconPanel.Paint += (s, e) => {
                // 绘制圆形
                e.Graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                using (var brush = new SolidBrush(iconPanel.BackColor))
                {
                    e.Graphics.FillEllipse(brush, 0, 0, 59, 59);
                }
                // 绘制图标文字
                using (var font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.White))
                {
                    var iconText = GetIconText(functionName);
                    var textSize = e.Graphics.MeasureString(iconText, font);
                    var textX = (60 - textSize.Width) / 2;
                    var textY = (60 - textSize.Height) / 2;
                    e.Graphics.DrawString(iconText, font, brush, textX, textY);
                }
            };

            // 创建功能名称标签
            var nameLabel = new LabelControl();
            nameLabel.Text = functionName;
            nameLabel.Appearance.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular);
            nameLabel.Appearance.ForeColor = Color.FromArgb(64, 64, 64);
            nameLabel.Appearance.Options.UseFont = true;
            nameLabel.Appearance.Options.UseForeColor = true;
            nameLabel.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            nameLabel.AutoSizeMode = LabelAutoSizeMode.None;
            nameLabel.Size = new Size(width, 20);
            nameLabel.Location = new Point(0, 75);

            // 添加点击事件
            buttonPanel.Click += (s, e) => ShowSystemManagementDialog(functionName);
            iconPanel.Click += (s, e) => ShowSystemManagementDialog(functionName);
            nameLabel.Click += (s, e) => ShowSystemManagementDialog(functionName);

            // 添加鼠标悬停效果
            buttonPanel.MouseEnter += (s, e) => {
                iconPanel.BackColor = Color.FromArgb(Math.Min(255, iconPanel.BackColor.R + 30),
                                                   Math.Min(255, iconPanel.BackColor.G + 30),
                                                   Math.Min(255, iconPanel.BackColor.B + 30));
                iconPanel.Invalidate();
            };
            buttonPanel.MouseLeave += (s, e) => {
                iconPanel.BackColor = GetIconColor(functionName);
                iconPanel.Invalidate();
            };

            buttonPanel.Controls.Add(iconPanel);
            buttonPanel.Controls.Add(nameLabel);
            panelContent.Controls.Add(buttonPanel);
        }

        private Color GetIconColor(string functionName)
        {
            // 根据功能名称返回不同的图标颜色
            switch (functionName)
            {
                case "挂号单设置": return Color.FromArgb(52, 152, 219);  // 蓝色
                case "门诊单设置": return Color.FromArgb(155, 89, 182);  // 紫色
                case "住院单设置": return Color.FromArgb(52, 152, 219);  // 蓝色
                case "数据库备份": return Color.FromArgb(155, 89, 182);  // 紫色
                case "科室管理": return Color.FromArgb(52, 152, 219);   // 蓝色
                case "权限管理": return Color.FromArgb(52, 152, 219);   // 蓝色
                case "员工添加": return Color.FromArgb(52, 152, 219);   // 蓝色
                case "数据恢复": return Color.FromArgb(52, 152, 219);   // 蓝色
                default: return Color.FromArgb(52, 152, 219);
            }
        }

        private string GetIconText(string functionName)
        {
            // 根据功能名称返回图标文字
            switch (functionName)
            {
                case "挂号单设置": return "挂";
                case "门诊单设置": return "门";
                case "住院单设置": return "住";
                case "数据库备份": return "备";
                case "科室管理": return "科";
                case "权限管理": return "权";
                case "员工添加": return "员";
                case "数据恢复": return "恢";
                default: return "?";
            }
        }

        private void ShowSystemManagementDialog(string functionName)
        {
            switch (functionName)
            {
                case "挂号单设置":
                    var registrationDesigner = new RegistrationFormDesigner();
                    registrationDesigner.ShowDialog();
                    break;
                case "住院单设置":
                    var hospitalizationDesigner = new HospitalizationFormDesigner();
                    hospitalizationDesigner.ShowDialog();
                    break;
                case "门诊单设置":
                    var outpatientDesigner = new OutpatientFormDesigner();
                    outpatientDesigner.ShowDialog();
                    break;
                case "科室管理":
                    var departmentManagement = new DepartmentManagementForm();
                    departmentManagement.ShowDialog();
                    break;
                default:
                    XtraMessageBox.Show($"这是{functionName}弹框", functionName,
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    break;
            }
        }

        private void CreateDoorDoctorPage()
        {
            CreatePageWithTitle("门诊医生页面", "这里是门诊医生工作台\n包括患者诊疗、病历管理、处方开具等功能");
        }

        private void CreateDoorManagementPage()
        {
            CreatePageWithTitle("门诊管理页面", "这里是门诊管理功能模块\n包括科室管理、医生排班、预约管理等功能");
        }

        private void CreateDoorChargePage()
        {
            CreatePageWithTitle("门诊收费页面", "这里是门诊收费功能模块\n包括费用结算、收费管理、发票打印等功能");
        }

        private void CreateHospitalManagementPage()
        {
            CreatePageWithTitle("住院管理页面", "这里是住院管理功能模块\n包括入院登记、床位管理、出院结算等功能");
        }

        private void CreateDrugManagementPage()
        {
            CreatePageWithTitle("药房管理页面", "这里是药房管理功能模块\n包括药品发放、库存管理、处方审核等功能");
        }

        private void CreateDrugStoragePage()
        {
            CreatePageWithTitle("药库管理页面", "这里是药库管理功能模块\n包括药品采购、入库管理、库存盘点等功能");
        }

        private void CreateFinanceManagementPage()
        {
            CreatePageWithTitle("财务管理页面", "这里是财务管理功能模块\n包括收支统计、财务报表、成本核算等功能");
        }

        private void CreatePageWithTitle(string title, string description)
        {
            // 创建主标题
            var titleLabel = new LabelControl();
            titleLabel.Text = title;
            titleLabel.Appearance.Font = new Font("Microsoft YaHei UI", 32F, FontStyle.Bold);
            titleLabel.Appearance.ForeColor = Color.FromArgb(54, 78, 111);
            titleLabel.Appearance.Options.UseFont = true;
            titleLabel.Appearance.Options.UseForeColor = true;
            titleLabel.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            titleLabel.AutoSizeMode = LabelAutoSizeMode.None;
            titleLabel.Size = new Size(600, 50);
            titleLabel.Location = new Point((panelContent.Width - 600) / 2, 100);
            titleLabel.Anchor = AnchorStyles.Top;

            // 创建描述文本
            var descLabel = new LabelControl();
            descLabel.Text = description;
            descLabel.Appearance.Font = new Font("Microsoft YaHei UI", 16F, FontStyle.Regular);
            descLabel.Appearance.ForeColor = Color.FromArgb(102, 102, 102);
            descLabel.Appearance.Options.UseFont = true;
            descLabel.Appearance.Options.UseForeColor = true;
            descLabel.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            descLabel.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            descLabel.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            descLabel.AutoSizeMode = LabelAutoSizeMode.None;
            descLabel.Size = new Size(500, 100);
            descLabel.Location = new Point((panelContent.Width - 500) / 2, 200);
            descLabel.Anchor = AnchorStyles.Top;

            panelContent.Controls.Add(titleLabel);
            panelContent.Controls.Add(descLabel);
        }

        // Dispose方法在Designer.cs中已定义，这里添加清理Timer的逻辑
        private void CleanupTimer()
        {
            timeTimer?.Stop();
            timeTimer?.Dispose();
        }
    }
}
